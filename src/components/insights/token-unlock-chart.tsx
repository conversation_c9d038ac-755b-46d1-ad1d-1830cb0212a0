"use client";

import { useMemo } from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  CHART_COLORS,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import { formatTokenAmount } from "~/lib/formatters";
import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

interface TokenUnlockChartProps {
  project: ProjectWithDetails;
}

export function TokenUnlockChart({ project }: TokenUnlockChartProps) {
  const { chartData, chartConfig, areaColors } = useMemo(() => {
    if (!project.allocationPools || project.allocationPools.length === 0) {
      return { chartData: [], chartConfig: {}, areaColors: {} };
    }

    // Create a simple mock timeline for demonstration
    const mockData = [
      { date: "Jan 2024" },
      { date: "Apr 2024" },
      { date: "Jul 2024" },
      { date: "Oct 2024" },
      { date: "Jan 2025" },
    ];

    // Build area colors and config
    const areaColors: Record<string, string> = {};
    const chartConfig: Record<string, { label: string; color: string }> = {};

    for (const [index, pool] of project.allocationPools.entries()) {
      const key = pool.name.toLowerCase().replaceAll(/\s+/g, "_");
      const color =
        CHART_COLORS[index % CHART_COLORS.length] || "var(--chart-1)";

      areaColors[key] = color;
      chartConfig[key] = {
        label: pool.name,
        color: color,
      };

      // Add mock progressive unlock data for each pool
      const poolTokens = pool.allocations.reduce(
        (sum, alloc) => sum + Number(alloc.tokenAmount),
        0
      );

      for (const [dataIndex, dataPoint] of mockData.entries()) {
        const unlockProgress = (dataIndex + 1) / mockData.length;
        (dataPoint as Record<string, unknown>)[key] =
          poolTokens * unlockProgress;
      }
    }

    return { chartData: mockData, chartConfig, areaColors };
  }, [project]);

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Token Unlock Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            No unlock schedule data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Token Unlock Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[400px] w-full">
          <AreaChart
            data={chartData}
            margin={{ top: 24, right: 24, left: 0, bottom: 0 }}
          >
            <defs>
              {Object.entries(areaColors).map(([key, color]) => (
                <linearGradient
                  id={`color-${key}`}
                  key={key}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={color} stopOpacity={0.2} />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
            <XAxis
              dataKey="date"
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatTokenAmount}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  valueFormatter={(value) => formatTokenAmount(Number(value))}
                />
              }
            />
            {Object.entries(areaColors).map(([key, color]) => (
              <Area
                key={key}
                type="monotone"
                dataKey={key}
                stackId="1"
                stroke={color}
                fill={`url(#color-${key})`}
                name={chartConfig[key]?.label || key}
              />
            ))}
            <ChartLegend
              content={<ChartLegendContent nameKey="label" />}
              verticalAlign="top"
              align="right"
              iconType="circle"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
