"use client";

import { useMemo } from "react";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { CHART_COLORS } from "~/components/ui/chart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { getStakeholderTypeColor, getPoolTypeColor } from "~/lib/colors";
import { formatTokenAmount, formatPercentage } from "~/lib/formatters";
import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

interface AllocationDetailsTableProps {
  project: ProjectWithDetails;
}

export function AllocationDetailsTable({
  project,
}: AllocationDetailsTableProps) {
  const groupedAllocations = useMemo(() => {
    if (!project.allocationPools || project.allocationPools.length === 0) {
      return [];
    }

    return project.allocationPools.map((pool, poolIndex) => ({
      pool,
      allocations: pool.allocations || [],
      color: CHART_COLORS[poolIndex % CHART_COLORS.length],
    }));
  }, [project.allocationPools]);

  if (groupedAllocations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Allocation Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            No allocation details available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Allocation Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="max-h-[500px] overflow-y-auto">
          <Table>
            <TableHeader className="bg-muted sticky top-0 z-20">
              <TableRow>
                <TableHead>Pool / Allocation</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Token Amount</TableHead>
                <TableHead className="text-right">Percentage</TableHead>
                <TableHead className="text-right">Vesting</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groupedAllocations.map(({ pool, allocations, color }) => (
                <>
                  {/* Pool Header Row */}
                  <TableRow key={`pool-${pool.id}`} className="border-b-2">
                    <TableCell className="font-bold">
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: color }}
                        />
                        {pool.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPoolTypeColor(pool.poolType)}>
                        {pool.poolType}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right font-bold">
                      {allocations.length > 0
                        ? formatTokenAmount(
                            allocations.reduce(
                              (sum, alloc) => sum + Number(alloc.tokenAmount),
                              0
                            )
                          )
                        : "-"}
                    </TableCell>
                    <TableCell className="text-right font-bold">
                      {formatPercentage(pool.allocationDecimal)}
                    </TableCell>
                    <TableCell className="text-right">-</TableCell>
                  </TableRow>

                  {/* Individual Allocations */}
                  {allocations.map((allocation) => (
                    <TableRow
                      key={allocation.allocationId}
                      className="border-l-4 border-l-transparent pl-4"
                    >
                      <TableCell className="pl-8">
                        <div className="text-sm">
                          {allocation.name || "Unnamed Allocation"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="secondary"
                          className={getStakeholderTypeColor(
                            allocation.stakeholderType
                          )}
                        >
                          {allocation.stakeholderType}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatTokenAmount(allocation.tokenAmount)}
                      </TableCell>
                      <TableCell className="text-right">
                        {allocation.totalTokenDecimal
                          ? formatPercentage(allocation.totalTokenDecimal)
                          : "-"}
                      </TableCell>
                      <TableCell className="text-right text-sm">
                        {allocation.vestingSchedules.length > 0
                          ? `${allocation.vestingSchedules.length} schedule${
                              allocation.vestingSchedules.length > 1 ? "s" : ""
                            }`
                          : "No vesting"}
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
