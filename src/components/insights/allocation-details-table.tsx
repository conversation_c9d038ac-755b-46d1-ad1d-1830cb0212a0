"use client";

import { useMemo } from "react";
import { Badge } from "~/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card";
import { CHART_COLORS } from "~/components/ui/chart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

interface AllocationDetailsTableProps {
  project: ProjectWithDetails;
}

function formatTokenAmount(amount: string | number | { toString(): string }) {
  const num =
    typeof amount === "object"
      ? Number.parseFloat(amount.toString())
      : typeof amount === "string"
        ? Number.parseFloat(amount)
        : amount;
  if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(2)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(2)}K`;
  }
  return num.toLocaleString();
}

function formatPercentage(decimal: string | number | { toString(): string }) {
  const num =
    typeof decimal === "object"
      ? Number.parseFloat(decimal.toString())
      : typeof decimal === "string"
        ? Number.parseFloat(decimal)
        : decimal;
  return `${(num * 100).toFixed(2)}%`;
}

function getStakeholderTypeBadgeColor(type: string) {
  switch (type.toLowerCase()) {
    case "founder": {
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300";
    }
    case "employee": {
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
    }
    case "investor": {
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
    }
    case "advisor": {
      return "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300";
    }
    case "consultant": {
      return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300";
    }
    default: {
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  }
}

export function AllocationDetailsTable({
  project,
}: AllocationDetailsTableProps) {
  const groupedAllocations = useMemo(() => {
    if (!project.allocationPools || project.allocationPools.length === 0) {
      return [];
    }

    return project.allocationPools.map((pool, poolIndex) => ({
      pool,
      allocations: pool.allocations || [],
      color: CHART_COLORS[poolIndex % CHART_COLORS.length],
    }));
  }, [project.allocationPools]);

  if (groupedAllocations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Allocation Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            No allocation details available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Allocation Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="max-h-[500px] overflow-y-auto">
          <Table>
            <TableHeader className="bg-muted sticky top-0 z-20">
              <TableRow>
                <TableHead>Pool / Allocation</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Token Amount</TableHead>
                <TableHead className="text-right">Percentage</TableHead>
                <TableHead className="text-right">Vesting</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groupedAllocations.map(({ pool, allocations, color }) => (
                <>
                  {/* Pool Header Row */}
                  <TableRow key={`pool-${pool.id}`} className="border-b-2">
                    <TableCell className="font-bold">
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: color }}
                        />
                        {pool.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{pool.poolType}</Badge>
                    </TableCell>
                    <TableCell className="text-right font-bold">
                      {allocations.length > 0
                        ? formatTokenAmount(
                            allocations.reduce(
                              (sum, alloc) => sum + Number(alloc.tokenAmount),
                              0
                            )
                          )
                        : "-"}
                    </TableCell>
                    <TableCell className="text-right font-bold">
                      {formatPercentage(pool.allocationDecimal)}
                    </TableCell>
                    <TableCell className="text-right">-</TableCell>
                  </TableRow>

                  {/* Individual Allocations */}
                  {allocations.map((allocation) => (
                    <TableRow
                      key={allocation.allocationId}
                      className="border-l-4 border-l-transparent pl-4"
                    >
                      <TableCell className="pl-8">
                        <div className="text-sm">
                          {allocation.name || "Unnamed Allocation"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="secondary"
                          className={getStakeholderTypeBadgeColor(
                            allocation.stakeholderType
                          )}
                        >
                          {allocation.stakeholderType}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatTokenAmount(allocation.tokenAmount)}
                      </TableCell>
                      <TableCell className="text-right">
                        {allocation.totalTokenDecimal
                          ? formatPercentage(allocation.totalTokenDecimal)
                          : "-"}
                      </TableCell>
                      <TableCell className="text-right text-sm">
                        {allocation.vestingSchedules.length > 0
                          ? `${allocation.vestingSchedules.length} schedule${
                              allocation.vestingSchedules.length > 1 ? "s" : ""
                            }`
                          : "No vesting"}
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
