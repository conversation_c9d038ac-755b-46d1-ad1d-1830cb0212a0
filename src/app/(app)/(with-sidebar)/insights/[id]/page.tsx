"use client";

import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { AllocationDetailsTable } from "~/components/insights/allocation-details-table";
import { AllocationPieChart } from "~/components/insights/allocation-pie-chart";
import { TokenUnlockChart } from "~/components/insights/token-unlock-chart";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { getIndustryColor } from "~/lib/colors";
import { formatTokenSupply } from "~/lib/formatters";
import { api } from "~/trpc/react";

export default function InsightsDetailPage() {
  const params = useParams();
  const projectId = params.id as string;

  const { data: project, error } = api.project.getById.useQuery(
    { id: projectId },
    { enabled: !!projectId }
  );

  if (error || !project) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/insights">
            <Button variant="ghost" size="default">
              <ArrowLeftIcon className="h-5 w-5" />
            </Button>
          </Link>
        </div>
        <div className="flex h-[400px] items-center justify-center text-red-500">
          {error?.message || "Project not found"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/insights">
            <Button variant="ghost" size="default">
              <ArrowLeftIcon className="size-4" />
            </Button>
          </Link>

          <h1 className="text-3xl font-semibold tracking-tight">
            {project.name || "Unnamed Project"}
          </h1>
          <div className="flex items-center gap-2">
            {project.symbol && (
              <Badge variant="outline">{project.symbol}</Badge>
            )}
            {project.industry && (
              <Badge className={getIndustryColor(project.industry)}>
                {project.industry}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Project Summary */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="rounded-lg border p-4">
          <div className="text-muted-foreground text-sm font-medium">
            Total Supply
          </div>
          <div className="text-2xl font-bold">
            {formatTokenSupply(project.totalTokenSupply)}
          </div>
        </div>
        <div className="rounded-lg border p-4">
          <div className="text-muted-foreground text-sm font-medium">
            Team Size
          </div>
          <div className="text-2xl font-bold">{project.teamSize || "N/A"}</div>
        </div>
        <div className="rounded-lg border p-4">
          <div className="text-muted-foreground text-sm font-medium">
            Allocation Pools
          </div>
          <div className="text-2xl font-bold">
            {project.allocationPools?.length || 0}
          </div>
        </div>
      </div>

      {/* Allocation Breakdown Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <AllocationPieChart project={project} />
        <AllocationDetailsTable project={project} />
      </div>

      {/* Token Unlock Schedule Section */}
      <TokenUnlockChart project={project} />
    </div>
  );
}
