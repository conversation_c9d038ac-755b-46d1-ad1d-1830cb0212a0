/**
 * Centralized formatting functions for consistent data display across the application
 */

// Number formatters
export const noDecimalFormatter = Intl.NumberFormat("en-US", {
  maximumFractionDigits: 0,
});

export const currencyFormatter = Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export const percentageFormatter = Intl.NumberFormat("en-US", {
  style: "percent",
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

// Token amount formatter with abbreviated units (K, M, B)
export function formatTokenAmount(
  amount: string | number | { toString(): string }
): string {
  const num =
    typeof amount === "object"
      ? Number.parseFloat(amount.toString())
      : typeof amount === "string"
        ? Number.parseFloat(amount)
        : amount;

  if (Number.isNaN(num)) return "0";

  if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(2)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(2)}K`;
  }
  return num.toLocaleString();
}

// Token supply formatter (similar to token amount but for bigint)
export function formatTokenSupply(supply: bigint | null): string {
  if (!supply) return "N/A";
  const num = Number(supply);
  if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(2)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(2)}K`;
  }
  return num.toLocaleString();
}

// Percentage formatter for decimal values (converts 0.25 to "25.00%")
export function formatPercentage(
  decimal: string | number | { toString(): string }
): string {
  const num =
    typeof decimal === "object"
      ? Number.parseFloat(decimal.toString())
      : typeof decimal === "string"
        ? Number.parseFloat(decimal)
        : decimal;

  if (Number.isNaN(num)) return "0.00%";
  return `${(num * 100).toFixed(2)}%`;
}

// Percentage formatter for already-percentage values (25 to "25.00%")
export function formatPercentageValue(
  percentage: string | number | { toString(): string }
): string {
  const num =
    typeof percentage === "object"
      ? Number.parseFloat(percentage.toString())
      : typeof percentage === "string"
        ? Number.parseFloat(percentage)
        : percentage;

  if (Number.isNaN(num)) return "0.00%";
  return `${num.toFixed(2)}%`;
}

// Date formatters
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) return "just now";
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86_400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 2_592_000)
    return `${Math.floor(diffInSeconds / 86_400)}d ago`;

  return formatDate(dateObj);
}

// File size formatter
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

// Duration formatter (for vesting periods, etc.)
export function formatDuration(durationInDays: number): string {
  if (durationInDays < 30) {
    return `${durationInDays} day${durationInDays === 1 ? "" : "s"}`;
  } else if (durationInDays < 365) {
    const months = Math.floor(durationInDays / 30);
    return `${months} month${months === 1 ? "" : "s"}`;
  } else {
    const years = Math.floor(durationInDays / 365);
    const remainingMonths = Math.floor((durationInDays % 365) / 30);
    if (remainingMonths === 0) {
      return `${years} year${years === 1 ? "" : "s"}`;
    }
    return `${years}y ${remainingMonths}m`;
  }
}
